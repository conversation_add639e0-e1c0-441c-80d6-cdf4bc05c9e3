{"name": "@tz-mobile/workspace", "version": "0.1.0", "private": true, "scripts": {"dev": "nx serve mobile-app", "build": "nx build mobile-app", "build:libs": "nx run-many --target=build --projects=tag:publishable", "build:packages": "nx run-many --target=build --projects=tag:package", "start": "nx start mobile-app", "lint": "nx lint mobile-app", "lint:all": "nx run-many --target=lint", "test": "nx run-many --target=test", "nx": "nx", "graph": "nx graph", "affected": "nx affected", "release": "nx release", "release:version": "nx release version", "release:publish": "nx release publish", "local-registry": "nx local-registry"}, "dependencies": {"@types/uuid": "^10.0.0", "antd-mobile": "^5.39.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.9.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@nx/eslint": "^21.1.2", "@nx/eslint-plugin": "^21.1.2", "@nx/jest": "^21.1.2", "@nx/js": "21.1.2", "@nx/next": "^21.1.2", "@nx/workspace": "^21.1.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "jsonc-eslint-parser": "^2.1.0", "nx": "^21.1.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5.6.3", "verdaccio": "^6.0.5"}, "nx": {"includedScripts": []}}