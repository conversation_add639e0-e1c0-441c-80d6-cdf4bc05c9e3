{"name": "feature-modules", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/feature-modules/src", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": ["scope:feature", "type:package", "publishable"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/feature-modules", "main": "packages/feature-modules/src/index.ts", "tsConfig": "packages/feature-modules/tsconfig.lib.json", "assets": ["packages/feature-modules/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}}}