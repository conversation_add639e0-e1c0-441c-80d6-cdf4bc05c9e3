{"name": "mobile-foundation", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/mobile-foundation/src", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": ["scope:mobile", "type:package", "publishable"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/mobile-foundation", "main": "packages/mobile-foundation/src/index.ts", "tsConfig": "packages/mobile-foundation/tsconfig.lib.json", "assets": ["packages/mobile-foundation/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}}}