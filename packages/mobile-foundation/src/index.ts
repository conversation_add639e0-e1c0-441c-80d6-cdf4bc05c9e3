/**
 * TZ移动端底座 - 主包入口
 *
 * 这是移动端底座的主包，聚合了所有核心功能模块：
 * - UI组件库：提供完整的移动端UI组件
 * - 功能模块：包含认证、表单、列表等业务功能
 * - 工具包：提供格式化、验证、辅助等工具函数
 *
 * @packageDocumentation
 */

// 导出主包核心功能
export * from './lib/mobile-foundation';

// 重新导出子包（当子包发布后可以启用）
// export * from '@tz-mobile/ui-components';
// export * from '@tz-mobile/feature-modules';
// export * from '@tz-mobile/utils-toolkit';

// 导出版本信息
export const VERSION = '0.1.0';

// 导出包信息
export const PACKAGE_INFO = {
  name: '@tz-mobile/mobile-foundation',
  version: VERSION,
  description: '基于Nx和Next.js的移动端底座',
  author: 'TZ Team',
  license: 'MIT',
  repository: 'https://github.com/tz-team/tz-mobile',
  homepage: 'https://github.com/tz-team/tz-mobile#readme',
  keywords: ['mobile', 'foundation', 'nextjs', 'nx', 'react', 'antd-mobile'],
} as const;
