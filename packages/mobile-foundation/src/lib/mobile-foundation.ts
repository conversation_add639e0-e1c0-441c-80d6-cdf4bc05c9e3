/**
 * 移动端底座核心功能
 */

/**
 * 初始化移动端底座
 * @param config 配置选项
 */
export interface MobileFoundationConfig {
  /** 应用名称 */
  appName?: string;
  /** 版本号 */
  version?: string;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 主题配置 */
  theme?: {
    primaryColor?: string;
    mode?: 'light' | 'dark' | 'auto';
  };
  /** API配置 */
  api?: {
    baseURL?: string;
    timeout?: number;
  };
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: Required<MobileFoundationConfig> = {
  appName: 'TZ移动端应用',
  version: '1.0.0',
  debug: false,
  theme: {
    primaryColor: '#1677ff',
    mode: 'light',
  },
  api: {
    baseURL: '/api',
    timeout: 10000,
  },
};

/**
 * 当前配置
 */
let currentConfig: Required<MobileFoundationConfig> = { ...DEFAULT_CONFIG };

/**
 * 初始化移动端底座
 * @param config 配置选项
 * @returns 初始化后的配置
 */
export function initMobileFoundation(config: MobileFoundationConfig = {}): Required<MobileFoundationConfig> {
  currentConfig = {
    ...DEFAULT_CONFIG,
    ...config,
    theme: {
      ...DEFAULT_CONFIG.theme,
      ...config.theme,
    },
    api: {
      ...DEFAULT_CONFIG.api,
      ...config.api,
    },
  };

  if (currentConfig.debug) {
    console.log('🚀 移动端底座初始化完成', currentConfig);
  }

  return currentConfig;
}

/**
 * 获取当前配置
 * @returns 当前配置
 */
export function getConfig(): Required<MobileFoundationConfig> {
  return { ...currentConfig };
}

/**
 * 更新配置
 * @param config 新的配置
 * @returns 更新后的配置
 */
export function updateConfig(config: Partial<MobileFoundationConfig>): Required<MobileFoundationConfig> {
  return initMobileFoundation({
    ...currentConfig,
    ...config,
  });
}

/**
 * 获取移动端底座信息
 * @returns 底座信息
 */
export function mobileFoundation(): string {
  return `TZ移动端底座 v${currentConfig.version}`;
}

/**
 * 检查是否为移动设备
 * @returns 是否为移动设备
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false;

  const userAgent = window.navigator.userAgent;
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

  return mobileRegex.test(userAgent);
}

/**
 * 获取设备信息
 * @returns 设备信息
 */
export function getDeviceInfo() {
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      userAgent: 'server',
      platform: 'server',
    };
  }

  const userAgent = window.navigator.userAgent;
  const isMobileDevice = isMobile();
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);

  return {
    isMobile: isMobileDevice && !isTablet,
    isTablet,
    isDesktop: !isMobileDevice && !isTablet,
    userAgent,
    platform: window.navigator.platform,
  };
}
