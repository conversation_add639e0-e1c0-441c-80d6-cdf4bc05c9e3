{"name": "ui-components", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ui-components/src", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": ["scope:ui", "type:package", "publishable"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/ui-components", "main": "packages/ui-components/src/index.ts", "tsConfig": "packages/ui-components/tsconfig.lib.json", "assets": ["packages/ui-components/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}}}