/**
 * 移动端底座应用提供者
 * 整合所有的全局配置和状态管理
 */

'use client';

import React from 'react';
import { ConfigProvider } from 'antd-mobile';
import { defaultTheme } from '../theme';
import { useGlobalUIStore } from '../store';

// 应用提供者属性接口
interface AppProvidersProps {
  children: React.ReactNode;
}

// Ant Design Mobile 配置提供者
const AntdMobileProvider: React.FC<AppProvidersProps> = ({ children }) => {
  const { themeMode } = useGlobalUIStore();

  // 根据主题模式配置Ant Design Mobile
  const antdConfig = {
    theme: {
      ...defaultTheme.antdMobile,
      // 根据主题模式调整颜色
      ...(themeMode === 'dark' && {
        backgroundColor: '#1f1f1f',
        componentBackground: '#2f2f2f',
        textColor: '#ffffff',
        textColorSecondary: '#cccccc',
        borderColor: '#434343',
      }),
    },
  };

  return (
    <ConfigProvider {...antdConfig}>
      {children}
    </ConfigProvider>
  );
};

// 主题提供者
const ThemeProvider: React.FC<AppProvidersProps> = ({ children }) => {
  const { themeMode } = useGlobalUIStore();

  React.useEffect(() => {
    // 设置HTML根元素的主题类名
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(themeMode);

    // 设置CSS变量
    const theme = defaultTheme.tailwind;
    Object.entries(theme.colors.primary).forEach(([key, value]) => {
      root.style.setProperty(`--color-primary-${key}`, value);
    });
    Object.entries(theme.colors.gray).forEach(([key, value]) => {
      root.style.setProperty(`--color-gray-${key}`, value);
    });
  }, [themeMode]);

  return <>{children}</>;
};

// 全局通知提供者
const NotificationProvider: React.FC<AppProvidersProps> = ({ children }) => {
  const { notifications, removeNotification } = useGlobalUIStore();

  return (
    <>
      {children}
      {/* 通知容器 */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`
              px-4 py-3 rounded-lg shadow-lg max-w-sm
              ${notification.type === 'success' ? 'bg-green-500 text-white' : ''}
              ${notification.type === 'error' ? 'bg-red-500 text-white' : ''}
              ${notification.type === 'warning' ? 'bg-yellow-500 text-white' : ''}
              ${notification.type === 'info' ? 'bg-blue-500 text-white' : ''}
            `}
            onClick={() => removeNotification(notification.id)}
          >
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{notification.message}</span>
              <button
                className="ml-2 text-white hover:text-gray-200"
                onClick={() => removeNotification(notification.id)}
              >
                ×
              </button>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

// 加载状态提供者
const LoadingProvider: React.FC<AppProvidersProps> = ({ children }) => {
  const { loading } = useGlobalUIStore();

  return (
    <>
      {children}
      {/* 全局加载遮罩 */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="mt-2 text-gray-600">加载中...</span>
          </div>
        </div>
      )}
    </>
  );
};

// 根应用提供者
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ThemeProvider>
      <AntdMobileProvider>
        <NotificationProvider>
          <LoadingProvider>
            {children}
          </LoadingProvider>
        </NotificationProvider>
      </AntdMobileProvider>
    </ThemeProvider>
  );
};

// 导出单独的提供者组件
export {
  AntdMobileProvider,
  ThemeProvider,
  NotificationProvider,
  LoadingProvider,
};
